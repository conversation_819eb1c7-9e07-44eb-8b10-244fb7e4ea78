#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量存储微服务实现

负责从Redis队列接收向量计算结果并存储到Milvus
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from fastapi import FastAPI, HTTPException
import redis.asyncio as redis
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import Config<PERSON>anager, Logger, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.utils import TimeUtils


@dataclass
class VectorResult:
    """向量计算结果"""
    task_id: str
    batch_id: str
    prov_id: int
    user_vectors: List[Dict[str, Any]]  # [{"uid": int, "user_vector": List[float], "provid": int}]
    batch_index: int
    total_batches: int
    processed_at: float
    stats: Dict[str, Any]


class VectorWriterService:
    """向量存储微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_service_config("vector_writer_service")
        self.logger = Logger(__name__, config_manager)
        self.exception_handler = ExceptionHandler(self.logger)
        
        # 初始化组件
        self.milvus_pool = None
        self.milvus_ops = None
        self.redis_client = None
        
        # 服务状态
        self.is_running = False
        self.is_processing = False
        self.stats = {
            "total_batches_processed": 0,
            "total_vectors_stored": 0,
            "failed_batches": 0,
            "start_time": time.time()
        }
        
        # 创建FastAPI应用
        self.app = FastAPI(title="Vector Writer Service", version="1.0.0")
        self._setup_routes()
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化向量存储微服务...")
            
            # 初始化Milvus连接池
            self.milvus_pool = MilvusPool(self.config_manager)
            await self.milvus_pool.initialize()
            self.milvus_ops = MilvusVectorOperations(self.milvus_pool, self.config_manager)
            
            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            self.logger.info("向量存储微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            if not self.is_running:
                raise HTTPException(status_code=503, detail="服务未就绪")
            
            return {
                "status": "healthy",
                "service": "vector_writer_service",
                "uptime": time.time() - self.stats["start_time"],
                "version": "1.0.0",
                "is_processing": self.is_processing
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取服务统计信息"""
            return {
                "stats": self.stats,
                "is_processing": self.is_processing
            }
        
        @self.app.get("/queue/status")
        async def get_queue_status():
            """获取队列状态"""
            try:
                redis_config = self.config.get("redis", {})
                queue_name = redis_config.get("storage_queue_name", "vector_storage_queue")
                
                queue_length = await self.redis_client.llen(queue_name)
                
                return {
                    "queue_name": queue_name,
                    "queue_length": queue_length,
                    "is_processing": self.is_processing
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取队列状态失败: {e}")
    
    async def start_queue_processing(self):
        """启动队列处理"""
        self.logger.info("启动队列处理...")
        asyncio.create_task(self._process_queue())
    
    async def _process_queue(self):
        """处理Redis队列中的任务"""
        redis_config = self.config.get("redis", {})
        queue_name = redis_config.get("storage_queue_name", "vector_storage_queue")
        batch_size = self.config.get("batch_processing", {}).get("batch_size", 10)
        
        self.logger.info(f"开始处理存储队列: {queue_name}")
        
        while self.is_running:
            try:
                # 批量获取任务
                tasks = []
                for _ in range(batch_size):
                    task_data = await self.redis_client.brpop(queue_name, timeout=1)
                    if task_data:
                        tasks.append(task_data[1])
                    else:
                        break
                
                if tasks:
                    self.is_processing = True
                    await self._process_batch_tasks(tasks)
                else:
                    self.is_processing = False
                    # 没有任务时短暂休眠
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                self.logger.error(f"队列处理错误: {e}")
                self.is_processing = False
                await asyncio.sleep(1)
    
    async def _process_batch_tasks(self, tasks: List[str]):
        """批量处理存储任务"""
        try:
            self.logger.info(f"开始批量处理存储任务: {len(tasks)} 个批次")
            
            # 解析所有任务
            vector_results = []
            for task_json in tasks:
                try:
                    result_dict = json.loads(task_json)
                    vector_result = VectorResult(**result_dict)
                    vector_results.append(vector_result)
                except Exception as e:
                    self.logger.error(f"解析向量结果失败: {e}")
                    self.stats["failed_batches"] += 1
                    continue
            
            if not vector_results:
                return
            
            # 收集所有用户向量
            all_user_vectors = []
            for result in vector_results:
                all_user_vectors.extend(result.user_vectors)
            
            if not all_user_vectors:
                self.logger.warning("没有找到有效的用户向量数据")
                return
            
            # 批量存储到Milvus
            stored_count = await self._store_user_vectors(all_user_vectors)
            
            # 更新统计
            self.stats["total_batches_processed"] += len(vector_results)
            self.stats["total_vectors_stored"] += stored_count
            
            self.logger.info(f"批量存储完成: 处理批次数={len(vector_results)}, 存储向量数={stored_count}")
            
        except Exception as e:
            self.stats["failed_batches"] += len(tasks)
            self.logger.error(f"批量处理存储任务失败: {e}")
            self.exception_handler.handle_exception(e)
    
    async def _store_user_vectors(self, user_vectors: List[Dict[str, Any]]) -> int:
        """存储用户向量到Milvus"""
        try:
            if not user_vectors:
                return 0
            
            # 获取用户向量集合配置
            milvus_config = self.config.get("milvus", {})
            user_collection = milvus_config.get("user_collection", "user_tower_collection")
            
            # 准备插入数据
            insert_data = {
                "user_id": [],
                "user_embedding": []
            }
            
            for user_vector in user_vectors:
                uid = user_vector["uid"]
                vector = user_vector["user_vector"]
                
                # 验证向量数据
                if not isinstance(vector, list) or len(vector) == 0:
                    self.logger.warning(f"用户 {uid} 的向量数据无效")
                    continue
                
                insert_data["user_id"].append(uid)
                insert_data["user_embedding"].append(vector)
            
            if not insert_data["user_id"]:
                self.logger.warning("没有有效的用户向量数据可以存储")
                return 0
            
            # 批量插入到Milvus
            await self.milvus_ops.insert_vectors(
                collection_name=user_collection,
                data=insert_data
            )
            
            stored_count = len(insert_data["user_id"])
            self.logger.debug(f"用户向量存储成功: 集合={user_collection}, 向量数={stored_count}")
            
            return stored_count
            
        except Exception as e:
            self.logger.error(f"存储用户向量失败: {e}")
            return 0
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.is_running = False
            
            if self.milvus_pool:
                await self.milvus_pool.close()
            
            if self.redis_client:
                await self.redis_client.close()
                
            self.logger.info("向量存储微服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")

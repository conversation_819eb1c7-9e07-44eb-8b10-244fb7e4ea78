#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB用户数据读取微服务主入口

提供HTTP API接口用于启动用户数据读取任务
"""

import os
import sys
import asyncio
import argparse
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger, ExceptionHandler
from .service import MongoDBReaderService


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MongoDB用户数据读取微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8003, help="服务监听端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        if args.config:
            config_manager.load_config_file(args.config)
        else:
            # 使用默认配置
            config_manager.load_service_config("mongodb_reader_service")
        
        # 初始化日志
        logger = Logger.get_logger("MongoDBReaderService")
        logger.info("=== MongoDB用户数据读取微服务启动 ===")
        
        # 创建服务实例
        service = MongoDBReaderService(config_manager)
        await service.initialize()
        
        # 启动服务
        import uvicorn
        uvicorn.run(
            service.app,
            host=args.host,
            port=args.port,
            log_level="info"
        )
        
    except Exception as e:
        logger = Logger.get_logger("MongoDBReaderService")
        logger.error(f"服务启动失败: {e}")
        ExceptionHandler.handle_exception(e)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

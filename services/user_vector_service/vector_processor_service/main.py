#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量计算微服务主入口

提供HTTP API接口和后台队列处理
"""

import os
import sys
import asyncio
import argparse
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, <PERSON><PERSON>, ExceptionHandler
from .service import VectorProcessorService


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="向量计算微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8004, help="服务监听端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        if args.config:
            config_manager.load_config_file(args.config)
        else:
            # 使用默认配置
            config_manager.load_service_config("vector_processor_service")
        
        # 初始化日志
        logger = Logger.get_logger("VectorProcessorService")
        logger.info("=== 向量计算微服务启动 ===")
        
        # 创建服务实例
        service = VectorProcessorService(config_manager)
        await service.initialize()
        
        # 启动后台队列处理
        asyncio.create_task(service.start_queue_processing())
        
        # 启动HTTP服务
        import uvicorn
        uvicorn.run(
            service.app,
            host=args.host,
            port=args.port,
            log_level="info"
        )
        
    except Exception as e:
        logger = Logger.get_logger("VectorProcessorService")
        logger.error(f"服务启动失败: {e}")
        ExceptionHandler.handle_exception(e)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

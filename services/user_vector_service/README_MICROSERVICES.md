# 用户向量服务 - 微服务架构

## 概述

本项目将原有的 `user_vector_service` 单体架构重构为异步微服务架构，实现了更好的可扩展性、可维护性和性能。

## 🎯 重构目标

- ✅ 将用户向量计算拆分为三个独立的异步微服务
- ✅ 支持较高的处理速度和并发能力
- ✅ 将监控程序单独作为一个脚本，提供统一的监控面板
- ✅ 完全移除所有与is_stored相关的操作
- ✅ 按省份集合名称进行筛选，无需查询语句筛选
- ✅ 使用Rich进行监控显示

## 🏗️ 架构设计

### 微服务组件

1. **MongoDB读取微服务** (`mongodb_reader_service`)

   - 端口: 8003
   - 职责: 从MongoDB读取用户数据、按省份筛选、发送到消息队列
   - 输出: 通过Redis队列发送用户批次数据
2. **向量计算微服务** (`vector_processor_service`)

   - 端口: 8004
   - 职责: 查询Milvus内容向量、计算用户向量、PCA降维
   - 输入: 从Redis队列接收用户数据
   - 输出: 通过Redis队列发送向量计算结果
3. **向量存储微服务** (`vector_writer_service`)

   - 端口: 8005
   - 职责: 接收向量计算结果、批量存储到Milvus
   - 输入: 从Redis队列接收向量数据
4. **监控服务** (`monitoring_service`)

   - 职责: 监控所有微服务状态、性能统计、Rich实时监控面板

### 通信机制

- **消息队列**: Redis
  - `vector_processing_queue`: MongoDB读取 → 向量计算
  - `vector_storage_queue`: 向量计算 → 向量存储
- **HTTP API**: 健康检查和统计接口
- **异步处理**: 支持高并发和非阻塞操作

## 📁 项目结构

```
services/user_vector_service/           # 用户向量服务（微服务版）
├── mongodb_reader_service/             # MongoDB读取微服务
│   ├── __init__.py
│   ├── main.py                         # 服务主入口
│   └── service.py                      # 服务实现
├── vector_processor_service/           # 向量计算微服务
│   ├── __init__.py
│   ├── main.py                         # 服务主入口
│   └── service.py                      # 服务实现
├── vector_writer_service/              # 向量存储微服务
│   ├── __init__.py
│   ├── main.py                         # 服务主入口
│   └── service.py                      # 服务实现
├── monitoring_service/                 # 监控服务
│   ├── __init__.py
│   └── monitor.py                      # 监控实现
├── start_services.py                   # 服务启动脚本
└── README_MICROSERVICES.md            # 微服务架构说明

configs/user_vector_service/            # 配置文件
├── mongodb_reader_service/
│   └── development.yaml
├── vector_processor_service/
│   └── development.yaml
├── vector_writer_service/
│   └── development.yaml
└── user_vector_monitoring_service/
    └── development.yaml
```

## 🚀 快速开始

### 启动所有服务

```bash
# 使用默认配置启动所有微服务
python3 services/user_vector_service/start_services.py

# 使用指定配置文件启动
python3 services/user_vector_service/start_services.py --config configs/user_vector_service/production.yaml

# 不使用tmux启动（直接启动进程）
python3 services/user_vector_service/start_services.py --no-tmux
```

### 检查服务状态

```bash
# 检查所有服务状态
python3 services/user_vector_service/start_services.py --status

# 或者通过HTTP API检查
curl http://localhost:8003/health  # MongoDB读取服务
curl http://localhost:8004/health  # 向量计算服务
curl http://localhost:8005/health  # 向量存储服务
```

### 停止服务

```bash
# 停止所有服务
python3 services/user_vector_service/start_services.py --stop
```

### 监控服务

```bash
# 实时监控面板（Rich界面）
python3 -m services.user_vector_service.monitoring_service.monitor --mode live

# 单次状态检查
python3 -m services.user_vector_service.monitoring_service.monitor --mode once
```

## 🔧 服务使用

### 启动用户向量计算任务

```bash
# 为省份100启动用户向量计算任务
curl -X POST "http://localhost:8003/read" \
  -H "Content-Type: application/json" \
  -d '{
    "prov_id": 100,
    "batch_size": 100000,
    "user_limit": 1000000
  }'
```

### 查看任务状态

```bash
# 查看任务状态
curl http://localhost:8003/task/{task_id}

# 查看服务统计
curl http://localhost:8003/stats
curl http://localhost:8004/stats
curl http://localhost:8005/stats
```

### 查看队列状态

```bash
# 查看队列状态
curl http://localhost:8004/queue/status
curl http://localhost:8005/queue/status

# 或者直接查看Redis
redis-cli llen vector_processing_queue
redis-cli llen vector_storage_queue
```

## 📊 数据流程

1. **MongoDB读取**: MongoDB读取微服务从指定省份的集合中读取用户数据
2. **队列传输**: 用户数据通过Redis队列传输到向量计算微服务
3. **向量计算**: 向量计算微服务查询Milvus获取内容向量，计算用户向量
4. **PCA降维**: 使用预计算的PCA模型进行512D→256D降维
5. **队列传输**: 向量结果通过Redis队列传输到向量存储微服务
6. **向量存储**: 向量存储微服务将用户向量批量存储到Milvus

## 🔍 监控和日志

### 实时监控

使用Rich库提供的实时监控面板：

```bash
python3 -m services.user_vector_service.monitoring_service.monitor --mode live
```

监控面板显示：

- 各微服务健康状态
- 处理统计信息
- 队列长度状态
- 响应时间监控

### 日志查看

```bash
# 通过tmux查看实时日志
tmux attach -t user-vector-mongodb-reader
tmux attach -t user-vector-processor
tmux attach -t user-vector-writer

# 查看日志文件
tail -f logs/user_vector_service/mongodb_reader_service/mongodb_reader_service.log
tail -f logs/user_vector_service/vector_processor_service/vector_processor_service.log
tail -f logs/user_vector_service/vector_writer_service/vector_writer_service.log
```

## ⚙️ 配置说明

### 主要配置项

- `mongodb`: MongoDB连接和集合配置
- `milvus`: Milvus向量数据库配置
- `redis`: Redis消息队列配置
- `batch_processing`: 批处理相关配置
- `vector_config`: 向量计算配置
- `pca_config`: PCA降维配置
- `logging`: 日志配置

### 队列控制

每个服务都支持队列长度控制，防止内存溢出：

```yaml
redis:
  queue_control:
    check_interval: 5
    max_queue_size: 100
    pause_threshold: 80
    resume_threshold: 20
```

## 🔄 与原架构的对比

### 主要改进

1. **架构解耦**: 将用户数据读取、向量计算、向量存储分离为独立服务
2. **异步通信**: 使用Redis消息队列实现服务间异步通信
3. **独立扩展**: 每个服务可以根据需要独立扩展
4. **统一监控**: 提供Rich实时监控面板和健康检查
5. **简化逻辑**: 完全移除is_stored相关操作
6. **省份筛选**: 通过集合名称直接筛选省份，无需查询条件

### 性能提升

- **并发处理**: 支持更高的并发处理能力
- **资源优化**: 更好的资源利用和内存管理
- **故障隔离**: 单个服务故障不影响整体系统
- **水平扩展**: 支持水平扩展以处理更大负载

## 🛠️ 开发和维护

### 添加新功能

1. 在相应的微服务中添加新的API端点
2. 更新配置文件
3. 添加相应的测试用例
4. 更新监控配置

### 故障排除

```bash
# 查看服务日志
tail -f logs/user_vector_service/*/service.log

# 检查Redis队列
redis-cli llen vector_processing_queue
redis-cli llen vector_storage_queue

# 检查服务进程
ps aux | grep "user_vector_service"
```

## 📚 API文档

### MongoDB读取微服务 (8003)

- `GET /health` - 健康检查
- `GET /stats` - 服务统计
- `POST /read` - 启动用户数据读取任务
- `GET /task/{task_id}` - 查看任务状态

### 向量计算微服务 (8004)

- `GET /health` - 健康检查
- `GET /stats` - 服务统计
- `GET /queue/status` - 队列状态

### 向量存储微服务 (8005)

- `GET /health` - 健康检查
- `GET /stats` - 服务统计
- `GET /queue/status` - 队列状态

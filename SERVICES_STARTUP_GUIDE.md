# 服务启动指南

本文档说明了统一后的服务启动方式。

## 概述

所有服务现在都使用统一的启动方式，参照 `user_vector_service` 的模式进行了重构。每个服务都有自己的 `start_services.py` 脚本，提供一致的命令行接口。

## 可用服务

### 1. ORC MongoDB 服务
- **位置**: `services/orc_mongodb_service/`
- **微服务**:
  - ORC处理微服务 (端口 8001)
  - MongoDB写入微服务 (端口 8002)

### 2. 用户向量服务
- **位置**: `services/user_vector_service/`
- **微服务**:
  - MongoDB读取微服务 (端口 8003)
  - 向量计算微服务 (端口 8004)
  - 向量存储微服务 (端口 8005)

## 统一的启动方式

每个服务都支持以下命令行参数：

```bash
# 查看帮助
python3 services/<service_name>/start_services.py --help

# 启动所有微服务（使用tmux）
python3 services/<service_name>/start_services.py

# 使用指定配置文件启动
python3 services/<service_name>/start_services.py --config path/to/config.yaml

# 直接启动（不使用tmux）
python3 services/<service_name>/start_services.py --no-tmux

# 检查服务状态
python3 services/<service_name>/start_services.py --status

# 停止所有服务
python3 services/<service_name>/start_services.py --stop
```

## 具体使用示例

### 启动 ORC MongoDB 服务

```bash
# 启动服务
python3 services/orc_mongodb_service/start_services.py

# 检查状态
python3 services/orc_mongodb_service/start_services.py --status

# 停止服务
python3 services/orc_mongodb_service/start_services.py --stop
```

### 启动用户向量服务

```bash
# 启动服务
python3 services/user_vector_service/start_services.py

# 检查状态
python3 services/user_vector_service/start_services.py --status

# 停止服务
python3 services/user_vector_service/start_services.py --stop
```

## 服务管理

### Tmux 会话管理

每个微服务都在独立的 tmux 会话中运行：

- **ORC MongoDB 服务**:
  - `orc-mongodb-processor` - ORC处理微服务
  - `orc-mongodb-writer` - MongoDB写入微服务

- **用户向量服务**:
  - `user-vector-mongodb-reader` - MongoDB读取微服务
  - `user-vector-processor` - 向量计算微服务
  - `user-vector-writer` - 向量存储微服务

### 查看服务日志

```bash
# 查看所有tmux会话
tmux list-sessions

# 连接到特定服务的会话
tmux attach -t <session-name>

# 例如：查看ORC处理服务日志
tmux attach -t orc-mongodb-processor
```

## 变更说明

1. **移除了 `scripts/` 目录**: 原有的全局启动脚本已被移除，每个服务现在都是自包含的。

2. **统一的接口**: 所有服务现在都使用相同的命令行参数和启动逻辑。

3. **改进的日志**: 使用统一的日志格式和颜色输出。

4. **更好的错误处理**: 改进了服务启动和停止的错误处理机制。

5. **状态检查**: 每个服务都支持 `--status` 参数来检查运行状态。

## 注意事项

- 确保在项目根目录下运行启动脚本
- 服务启动需要相应的配置文件存在
- 使用 `Ctrl+C` 可以优雅地停止服务
- 如果端口被占用，启动脚本会尝试自动清理

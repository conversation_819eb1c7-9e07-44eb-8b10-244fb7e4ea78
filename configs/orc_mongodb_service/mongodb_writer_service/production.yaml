# MongoDB写入微服务 - 开发环境配置
# 版本: 1.0.0

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "1.0.0"
  environment: "development"

# ==================== 服务配置 ====================
service:
  name: "mongodb_writer_service"
  host: "0.0.0.0"
  port: 42002
  workers: 1

# ==================== MongoDB配置 ====================
mongodb:
  # 连接配置
  connection:
    use_uri: false
    
    # 分离参数连接
    host: "localhost"
    port: 27017
    username: ""
    password: ""
    auth_source: "admin"
  
  # 数据库名称
  database: "nrdc"

  # 基础集合名称（实际集合名称会根据provid动态生成，如：user_pid_records_100）
  collection: "user_pid_records"
  
  # 连接池配置
  connection_pool:
    max_pool_size: 50
    min_pool_size: 10
    max_idle_time_ms: 30000
    max_life_time_ms: 600000

  # 写入性能优化配置
  write_optimization:
    write_concern:
      w: 1
      j: false
      wtimeout: 30000

    bulk_operations:
      unordered_inserts: false
      bypass_document_validation: false

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""
  
  # 队列配置
  queue_name: "mongodb_write_queue"
  batch_timeout: 5  # 批量获取超时时间（秒）
  
  # 连接池配置
  connection_pool:
    max_connections: 20
    retry_on_timeout: true

# ==================== 批处理配置 ====================
batch_processing:
  # 从队列批量获取任务的大小
  batch_size: 10
  # MongoDB写入批次大小
  write_batch_size: 1000
  # 处理超时时间（秒）
  processing_timeout: 300

# ==================== 重试配置 ====================
retry:
  # 最大重试次数
  max_retries: 3
  # 重试延迟（秒）
  delay: 5
  # 指数退避因子
  backoff_factor: 2.0

# ==================== 清理配置 ====================
cleanup:
  # 任务完成后保留时间（秒）
  delay: 300
  # 定期清理间隔（秒）
  interval: 600

# ==================== 监控和日志配置 ====================
# 进度报告间隔（秒）
progress_report_interval: 30

# 统计输出间隔（秒）
stats_output_interval: 60

# 是否启用详细统计
enable_detailed_stats: true

# 是否启用详细日志
enable_verbose_logging: true

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/mongodb_writer_service/mongodb_writer_service.log
  file_max_size: "100MB"
  file_backup_count: 5
  console_enabled: true
  console_colored: false
  structured: false

# ==================== 性能优化配置 ====================
performance:
  # 内存优化
  memory:
    gc_threshold: 0.8
    cleanup_interval: 300
  
  # 并发配置
  concurrency:
    max_concurrent_writes: 10
    max_queue_workers: 5

# ==================== 健康检查配置 ====================
health_check:
  # 检查间隔（秒）
  interval: 30
  # 超时时间（秒）
  timeout: 10
  # MongoDB连接检查
  check_mongodb: true
  # Redis连接检查
  check_redis: true

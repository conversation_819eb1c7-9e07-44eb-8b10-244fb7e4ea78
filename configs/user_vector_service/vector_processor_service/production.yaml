# 向量计算微服务 - 开发环境配置
# 版本: 1.0.0

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "1.0.0"
  environment: "development"

# ==================== 服务配置 ====================
service:
  name: "vector_processor_service"
  port: 42004

# ==================== Milvus配置 ====================
milvus:
  # 连接配置
  connection:
    uri: "http://************:19530"  # 生产环境
    token: "nrdc_ilm:Nr@#dc12Ilm"
    database: "nrdc_db"
    
  # 连接池配置
  pool:
    max_connections: 20
    connection_timeout: 30
    
  # 集合配置
  content_collection: "content_tower_collection_20250616"
  user_collection: "user_tower_collection"
  
  # 查询配置
  query:
    batch_size: 15000
    timeout: 30
    consistency_level: "Eventually"

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""
  
  # 队列配置
  vector_queue_name: "vector_processing_queue"
  storage_queue_name: "vector_storage_queue"
  
  # 队列长度控制配置
  queue_control:
    # 队列长度检查间隔（秒）
    check_interval: 5
    # 最大存储队列长度
    max_storage_queue_size: 50
    # 暂停阈值：队列长度超过此值时暂停发送
    pause_threshold: 40
    # 恢复阈值：队列长度低于此值时恢复发送
    resume_threshold: 10

# ==================== 向量计算配置 ====================
vector_config:
  # 最少PID要求（用户必须有至少这么多PID才能计算向量）
  min_pids_required: 3
  # 计算向量时使用的最大PID数量
  max_pids_for_computation: 200
  # 向量聚合方法
  aggregation_method: "weighted_mean"  # weighted_mean, simple_mean, max_pooling
  # 权重策略
  weight_strategy: "uniform"  # uniform, recency, frequency
  # 时间衰减因子
  time_decay_factor: 0.1
  # 最大时间权重
  max_time_weight: 2.0

# ==================== PCA配置 ====================
pca_config:
  # PCA方法（使用预计算模型）
  pca_method: "precomputed_pca"
  # 预计算PCA模型路径
  precomputed_pca_model_path: "models/pca_precomputed/latest_pca_model.pkl"
  # 目标向量维度（降维后）
  target_dimension: 256
  # 源向量维度（内容向量维度）
  source_dimension: 512
  # 是否启用自动重载模型
  auto_reload: false
  # 模型检查间隔（秒）
  check_interval: 300
  # 随机状态种子（用于预计算训练）
  random_state: 42

# ==================== 批处理配置 ====================
batch_processing:
  # 向量查询批次大小
  vector_batch_size: 15000
  # 处理超时时间（秒）
  processing_timeout: 60
  # 批次处理间隔（毫秒）
  batch_process_interval: 100

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/user_vector_service/vector_processor_service/vector_processor_service.log
  file_max_size: "100MB"
  file_backup_count: 5
  console_enabled: true
  console_colored: false
  structured: false

# ==================== 监控配置 ====================
monitoring:
  # 是否启用监控
  enabled: true
  # 统计报告间隔（秒）
  stats_report_interval: 30
  # 性能监控间隔（秒）
  performance_monitor_interval: 60
